<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\Client\Constants\DomainStatus;
use App\Modules\RequestDelete\Constants\StatusTypes;
use App\Modules\RequestDelete\Requests\ShowListRequest;
use App\Traits\CursorPaginate;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Modules\CustomLogger\Services\AuthLogger;

class DatabaseQueryService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    public static function instance()
    {
        $DatabaseQueryService = new self;

        return $DatabaseQueryService;
    }

    public function get(ShowListRequest $request)
    {
        $pageLimit = $request->input('limit', self::$pageLimit);
        $builder = self::baseQuery();
        self::whenHasDomain($builder, $request);
        self::whenHasEmail($builder, $request);
        self::whenHasStatusType($builder, $request);
        self::whenHasOrderby($builder, $request);
        $builder = $builder->paginate($pageLimit)->withQueryString();
        return [
            ...CursorPaginate::cursor($builder, self::paramToURI($request)),
            "search" => $request->search ?? ""
        ];
    }
    public function supportNoteSave(ShowListRequest $request)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->where('id', $request->deletion_id)
            ->update([
                'support_note' => $request->support_note,
                'support_agent_name' => Auth::user()->name . ' (' . Auth::user()->email . ')',
                'feedback_date' => now(),
            ]);
        //return CursorPaginate::cursor($builder, self::paramToURI($request));
    }

    public function getDomainInfo($domainId)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->join('registered_domains', 'domain_cancellation_requests.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->select([
                'domains.id as domainId',
                'domains.name as domainName',
                'users.id as userID',
                'users.email as userEmail',
                'users.first_name',
                'users.last_name',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.requested_at'
            ])
            ->where('domains.id', $domainId)
            ->first();
    }

    public function getExpiredApprovedRequests()
    {
        return DB::client()->table('domain_cancellation_requests')
            ->join('registered_domains', 'domain_cancellation_requests.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->select([
                'domains.id as domainId',
                'domains.name as domainName',
                'domains.status as domainStatus',
                'users.id as userID',
                'users.email as userEmail',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.feedback_date as approvedDate',
                'domain_cancellation_requests.support_agent_id',
                'domain_cancellation_requests.support_agent_name'
            ])
            ->where('domains.status', 'IN_PROCESS')
            ->whereNotNull('domain_cancellation_requests.support_agent_id')
            ->whereNotNull('domain_cancellation_requests.deleted_at')
            ->whereNotNull('domain_cancellation_requests.feedback_date')
            ->where('domain_cancellation_requests.feedback_date', '<=', now()->subHours(24))
            ->limit(500)
            ->get();
    }

    public function getDomainDetailsForRefund($domainId)
    {
        return DB::client()->table('domains')
            ->where('id', $domainId)
            ->select('created_at', 'client_renew_at')
            ->first();
    }

    public function getRequestDetailsForRenewal($registeredDomainId)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->select('requested_at')
            ->first();
    }

    public function updateDomainCancellationRequest($registeredDomainId, array $updateData)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->update($updateData);
    }

    public function createDomainCancellationRequest(array $insertData)
    {
        return DB::client()->table('domain_cancellation_requests')->insert($insertData);
    }

    public function calculateRefundAndRenewalStatus($domainId, $requestedAt = null)
    {
        $requestedAt = $requestedAt ? Carbon::parse($requestedAt) : Carbon::now();

        $domain = DB::client()->table('domains')
            ->where('id', $domainId)
            ->select('created_at', 'client_renew_at', 'expiry')
            ->first();

        $domainCreatedAt = Carbon::parse($domain->created_at);
        $expiryTimestamp = $domain->expiry / 1000;
        $expiryDate = Carbon::createFromTimestamp($expiryTimestamp);

        $is_refunded = $domainCreatedAt->diffInDays($requestedAt) >= 5;
    
        $yearsBetweenCreatedAndExpiry = $domainCreatedAt->diffInYears($expiryDate);

        $registeredDomain = DB::client()->table('registered_domains')
            ->where('domain_id', $domainId)
            ->first();

        $hasRenewalPayments = false;
        if ($registeredDomain) {
            $hasRenewalPayments = DB::client()->table('payment_nodes')
                ->join('extension_fees', 'payment_nodes.extension_fee_id', '=', 'extension_fees.id')
                ->join('fees', 'extension_fees.fee_id', '=', 'fees.id')
                ->where('payment_nodes.registered_domain_id', $registeredDomain->id)
                ->where('fees.type', 'RENEW')
                ->where('payment_nodes.created_at', '>', $domainCreatedAt->copy()->addDays(4)) // Renewal must be after 4 days of creation
                ->exists();
        }

        // is_renewal should be false if client renewed within 4 days, otherwise true
        // We determine this by checking if there are actual renewal payments after the grace period
        $is_renewal = !$hasRenewalPayments || $yearsBetweenCreatedAndExpiry <= 1;

        return [
            'is_refunded' => $is_refunded,
            'is_renewal' => $is_renewal
        ];
    }

    // PRIVATE Functions

    private function baseQuery(): Builder
    {
        return DB::client()->table('domain_cancellation_requests')
            ->join('registered_domains', 'domain_cancellation_requests.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->select(self::getSelectFields());
    }

    private function getSelectFields(): array
    {
        return [
            'domains.name as domainName',
            'domains.status',
            'domains.deleted_at as domainDeletedAt',
            'domains.created_at',
            'domains.id as domain_id',
            'users.id as user_id',
            'users.email',
            'users.first_name',
            'users.last_name',
            'domain_cancellation_requests.deleted_at',
            'domain_cancellation_requests.requested_at',
            'domain_cancellation_requests.reason',
            'domain_cancellation_requests.id as dcrID',
            'domain_cancellation_requests.support_agent_id',
            'domain_cancellation_requests.support_agent_name',
            'domain_cancellation_requests.support_note',
            'domain_cancellation_requests.feedback_date',
            'domain_cancellation_requests.registered_domain_id',
            'registered_domains.status as rstatus'
        ];
    }

    private function whenHasStatusType(Builder &$builder, ShowListRequest $request): void
    {
        if (!$request->has('statusType')) {
            return;
        }

        match ($request->statusType) {
            StatusTypes::ALL => null,
            StatusTypes::PENDING => $builder->whereNull('domain_cancellation_requests.deleted_at')
                ->whereNull('domain_cancellation_requests.feedback_date'),
            StatusTypes::APPROVED => $builder->whereNotNull('domain_cancellation_requests.deleted_at')
                ->whereNotNull('domain_cancellation_requests.feedback_date')
                ->where('domains.status', DomainStatus::IN_PROCESS),
            StatusTypes::REJECTED => $builder->whereNull('domain_cancellation_requests.deleted_at')
                ->whereNotNull('domain_cancellation_requests.feedback_date')
                ->where(function ($q) {
                        $q->where('domains.status', DomainStatus::ACTIVE)
                        ->orWhere('domains.status', DomainStatus::EXPIRED);
                    }),
            StatusTypes::CANCELLED => $builder->whereNotNull('domain_cancellation_requests.deleted_at')
                ->whereNull('domain_cancellation_requests.feedback_date')
                ->where(function ($q) {
                        $q->where('domains.status', DomainStatus::ACTIVE)
                        ->orWhere('domains.status', DomainStatus::EXPIRED);
                    }),
            StatusTypes::DELETED => $builder->where('domains.status', DomainStatus::DELETED),
            default => $builder->whereNull('domain_cancellation_requests.deleted_at')
        };
    }

    private function whenHasOrderby(Builder &$builder, ShowListRequest $request): void
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);

            if (count($orderby) == 2 && in_array($orderby[1], ['asc', 'desc'])) {
                switch ($orderby[0]) {
                    case 'domain':
                        $query->orderBy('domainName', $orderby[1]);
                        break;
                    case 'requested_at':
                        $query->orderBy('domain_cancellation_requests.requested_at', $orderby[1]);
                        break;
                    default:
                        $query->orderBy('dcrID', 'desc');
                }
            } else {
                $query->orderBy('dcrID', 'desc');
            }
        })
            ->when(!$request->has('orderby'), function (Builder $query) {
                $query->orderBy('dcrID', 'desc');
            });
    }

    private static function whenHasDomain(&$builder, $request)
    {
        $builder->when(($request->has('domain') || $request->has('search')), function (Builder $query) use ($request) {
            $domain = $request->domain ?? $request->search;
            $query->where('name', 'ilike', $domain . '%');
        });
    }

    private static function whenHasEmail(&$builder, $request)
    {
        $builder->when($request->has('email'), function (Builder $query) use ($request) {
            $email = $request->email;
            $query->where('users.email', 'ilike', $email . '%');
        });
    }

    private function paramToURI(ShowListRequest $request): array
    {
        $param = [];

        if ($request->has('statusType')) {
            $param[] = 'statusType=' . $request->statusType;
        }

        if ($request->has('orderby')) {
            $param[] = 'orderby=' . $request->orderby;
        }

        return $param;
    }
}
